' modMouseWheel.bas
Option Explicit

#If VBA7 Then
    ' 64位API声明
    Private Declare PtrSafe Function SetWindowLong Lib "user32" Alias "SetWindowLongA" _
        (ByVal hWnd As LongPtr, ByVal nIndex As Long, ByVal dwNewLong As LongPtr) As LongPtr
    Private Declare PtrSafe Function CallWindowProc Lib "user32" Alias "CallWindowProcA" _
        (ByVal lpPrevWndFunc As LongPtr, ByVal hWnd As LongPtr, ByVal Msg As Long, _
        ByVal wParam As LongPtr, ByVal lParam As LongPtr) As LongPtr
    Private Declare PtrSafe Function GetFocus Lib "user32" () As LongPtr
#Else
    ' 32位API声明
    Private Declare Function SetWindowLong Lib "user32" Alias "SetWindowLongA" _
        (ByVal hWnd As Long, ByVal nIndex As Long, ByVal dwNewLong As Long) As Long
    Private Declare Function CallWindowProc Lib "user32" Alias "CallWindowProcA" _
        (ByVal lpPrevWndFunc As Long, ByVal hWnd As <PERSON>, ByVal Msg <PERSON>, _
        ByVal wParam As Long, ByVal lParam As Long) As Long
    Private Declare Function GetFocus Lib "user32" () As Long
#End If

' 常量定义
Private Const GWL_WNDPROC As Long = -4
Private Const WM_MOUSEWHEEL As Long = &H20A
Private Const EM_LINESCROLL As Long = &HB6

' 钩子信息存储
Private Type HookInfo
    ControlHwnd As LongPtr
    OriginalProc As LongPtr
    ScrollStep As Integer
End Type
Private HookCollection() As HookInfo
Private HookCount As Long

' ================= 公共接口 =================
Public Sub InitializeHooks()
    HookCount = 0
    Erase HookCollection
End Sub

Public Sub EnableMouseWheel(txtBox As Object)
    On Error Resume Next
    ' 添加类型检查确保是文本框
    If TypeName(txtBox) <> "TextBox" Then
        MsgBox "错误：只支持文本框控件", vbExclamation
        Exit Sub
    End If
    
    ' 添加句柄有效性检查
    If txtBox.hWnd = 0 Then
        MsgBox "错误：控件句柄无效", vbExclamation
        Exit Sub
    End If
    
    #If VBA7 Then
        Dim hWnd As LongPtr
    #Else
        Dim hWnd As Long
    #End If
    
    ' 获取控件句柄（确保控件已创建）
    hWnd = txtBox.hWnd
    If hWnd = 0 Then Exit Sub
    
    ' 跳过已挂钩控件
    If IsHooked(hWnd) Then Exit Sub
    
    ' 保存钩子信息
    HookCount = HookCount + 1
    ReDim Preserve HookCollection(1 To HookCount)
    
    With HookCollection(HookCount)
        .ControlHwnd = hWnd
        .ScrollStep = GetScrollStep(txtBox) ' 动态紌
        
        #If VBA7 Then
            .OriginalProc = SetWindowLong(hWnd, GWL_WNDPROC, AddressOf WindowProc)
        #Else
            .OriginalProc = SetWindowLong(hWnd, GWL_WNDPROC, AddressOf WindowProc)
        #End If
    End With
End Sub

Public Sub CleanupHooks()
    Dim i As Long
    For i = 1 To HookCount
        If HookCollection(i).ControlHwnd <> 0 Then
            #If VBA7 Then
                SetWindowLong HookCollection(i).ControlHwnd, GWL_WNDPROC, HookCollection(i).OriginalProc
            #Else
                SetWindowLong HookCollection(i).ControlHwnd, GWL_WNDPROC, HookCollection(i).OriginalProc
            #End If
        End If
    Next i
    InitializeHooks
End Sub

' ================= 核心处理逻辑 =================
#If VBA7 Then
Private Function WindowProc( _
    ByVal hWnd As LongPtr, _
    ByVal uMsg As Long, _
    ByVal wParam As LongPtr, _
    ByVal lParam As LongPtr _
) As LongPtr
#Else
Private Function WindowProc( _
    ByVal hWnd As Long, _
    ByVal uMsg As Long, _
    ByVal wParam As Long, _
    ByVal lParam As Long _
) As Long
#End If
    If uMsg = WM_MOUSEWHEEL Then
        ' 获取当前焦点控件
        #If VBA7 Then
            Dim focusedHwnd As LongPtr
        #Else
            Dim focusedHwnd As Long
        #End If
        focusedHwnd = GetFocus()
        
        ' 查找匹配的钩子
        Dim i As Long
        For i = 1 To HookCount
            If HookCollection(i).ControlHwnd = focusedHwnd Then
                ' 计算滚动方向（delta为正表示向上滚动）
                Dim delta As Integer
                #If VBA7 Then
                    delta = wParam \ &H10000 \ 120  ' 标准化滚动量
                #Else
                    delta = wParam \ 65536 \ 120
                #End If
                
                ' 发送滚动消息（更可靠的滚动方式）[7](@ref)
                #If VBA7 Then
                    SendMessage hWnd, EM_LINESCROLL, 0, ByVal delta * HookCollection(i).ScrollStep
                #Else
                    SendMessage hWnd, EM_LINESCROLL, 0, ByVal CLng(delta * HookCollection(i).ScrollStep)
                #End If
                
                ' 返回0表示已处理消息
                WindowProc = 0
                Exit Function
            End If
        Next i
    End If
    
    ' 其他消息传递给原始处理函数
    For i = 1 To HookCount
        If HookCollection(i).ControlHwnd = hWnd Then
            #If VBA7 Then
                WindowProc = CallWindowProc(HookCollection(i).OriginalProc, hWnd, uMsg, wParam, lParam)
            #Else
                WindowProc = CallWindowProc(HookCollection(i).OriginalProc, hWnd, uMsg, wParam, lParam)
            #End If
            Exit For
        End If
    Next i
End Function

' ================= 辅助函数 =================
Private Function IsHooked(hWnd As LongPtr) As Boolean
    Dim i As Long
    For i = 1 To HookCount
        If HookCollection(i).ControlHwnd = hWnd Then
            IsHooked = True
            Exit Function
        End If
    Next i
    IsHooked = False
End Function

Private Function GetScrollStep(txtBox As MSForms.TextBox) As Integer
    ' 根据字体大小动态计算滚动步长（宋体9号≈15px）[1](@ref)
    Select Case txtBox.Font.Size
        Case 8: GetScrollStep = -3  ' 向上滚动3行
        Case 9: GetScrollStep = -4
        Case 10: GetScrollStep = -5
        Case Else: GetScrollStep = -txtBox.Font.Size * 0.5
    End Select
    
    ' 宋体额外调整
    If InStr(1, txtBox.Font.Name, "宋体", vbTextCompare) > 0 Then
        GetScrollStep = GetScrollStep - 1
    End If
End Function

